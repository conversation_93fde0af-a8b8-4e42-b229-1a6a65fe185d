/* Navigation Section Styles */
.navigation {
  position: relative;
  padding: 16px 0;
}

.navigation-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.navigation-tiles {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  overflow-x: auto;
  padding: 8px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-tiles::-webkit-scrollbar {
  display: none;
}

.nav-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
  display: block;
  filter: hue-rotate(12deg) brightness(1.2);
}

.nav-image:hover {
  opacity: 1;
  transform: scale(1.02);
}

.nav-image:active {
  transform: scale(0.95);
}

/* Focus styles for accessibility */
.nav-image:focus {
  outline: 2px solid rgb(var(--primary-color));
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .navigation-tiles {
    gap: 14px;
  }
}

@media (max-width: 1024px) {
  .navigation-tiles {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .navigation {
    padding: 20px 0;
  }

  .navigation-container {
    padding: 0 15px;
  }

  .navigation-tiles {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .nav-image {
    width: 100%;
    height: auto;
  }
}

@media (max-width: 480px) {
  .navigation {
    padding: 15px 0;
  }

  .navigation-tiles {
    gap: 12px;
  }
}

/* Enhanced Focus Styles for Accessibility */
.nav-image:focus-visible {
  outline: 3px solid rgb(var(--primary-color));
  outline-offset: 3px;
  opacity: 1;
  transform: scale(1.05);
}

/* Highlight Games Animation */
.navigation.highlight-games {
  animation: highlightPulse 4s ease-in-out;
  position: relative;
  z-index: 10;
}

.navigation.highlight-games::before {
  content: '';
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  background: linear-gradient(45deg,
    rgba(255, 215, 0, 0.4),
    rgba(255, 165, 0, 0.4),
    rgba(255, 215, 0, 0.4),
    rgba(255, 140, 0, 0.4)
  );
  border-radius: 20px;
  z-index: -1;
  animation: highlightGlow 4s ease-in-out;
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.navigation.highlight-games .navigation-tiles {
  animation: highlightBounce 4s ease-in-out;
}

.navigation.highlight-games .nav-image-container,
.navigation.highlight-games .nav-image {
  animation: highlightShimmer 4s ease-in-out;
  transform: scale(1.05);
}

@keyframes highlightPulse {
  0%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.02);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes highlightGlow {
  0%, 100% {
    opacity: 0;
    box-shadow: 0 0 0 rgba(255, 215, 0, 0);
  }
  25% {
    opacity: 0.6;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
  }
  75% {
    opacity: 0.6;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
  }
}

@keyframes highlightBounce {
  0%, 100% {
    transform: translateY(0);
  }
  25%, 75% {
    transform: translateY(-5px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes highlightShimmer {
  0%, 100% {
    filter: hue-rotate(12deg) brightness(1.2);
  }
  25%, 75% {
    filter: hue-rotate(12deg) brightness(1.4) saturate(1.2);
  }
  50% {
    filter: hue-rotate(12deg) brightness(1.6) saturate(1.4);
  }
}
