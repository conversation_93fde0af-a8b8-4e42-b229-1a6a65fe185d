// Hero Section Slider JavaScript
window.$LANDING.onLifecycle({ selector: `.makrobet-landing`, page: /^\/\w+$/, onMount: (_, __, kill) => {
  // Configuration
  const SLIDER_TYPE = 34
  const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1
  const USE_MOCKED_DATA = false; // Set to false to use API
  const SLIDER_API_URL = `https://pn17.pfnow.net/api/tr/consumer`;
  const IMAGE_BASE_URL = `https://cdn.pfcdn100.com/merchants/pn17/uploads/`;
  const SLIDE_DURATION = 5000; // 5 seconds per slide


  // Mocked slider data
  const MOCKED_SLIDES = [
    {
      id: 1,
      path: `/cdn/makrobet/upload_files/100cevrimsiz.png`,
      url: `/contents/promotions`,
      content: `%100 Çevrimsiz Bonus`,
      type: 1
    },
    {
      id: 2,
      path: `/cdn/makrobet/upload_files/anlikkayip.png`,
      url: `/contents/promotions`,
      content: `<PERSON><PERSON><PERSON><PERSON>`,
      type: 1
    },
    {
      id: 3,
      path: `/cdn/makrobet/upload_files/deneme250.png`,
      url: `/contents/promotions`,
      content: `250 TL Deneme Bonusu`,
      type: 1
    },
    {
      id: 4,
      path: `/cdn/makrobet/upload_files/haftalikdc.png`,
      url: `/contents/promotions`,
      content: `Haftalık Discount`,
      type: 1
    },
    {
      id: 5,
      path: `/cdn/makrobet/upload_files/WEBSLIDERFIFA.png`,
      url: `/contents/promotions`,
      content: `FIFA`,
      type: 1
    },
    {
      id: 6,
      path: `/cdn/makrobet/upload_files/klasiklere-donus-920x400.png`,
      url: `/contents/promotions`,
      content: `klasiklere douns`,
      type: 1
    },
    {
      id: 7,
      path: `/cdn/makrobet/upload_files/pragmatic-920x400.png`,
      url: `/contents/promotions`,
      content: `pragmatic`,
      type: 1
    },
  ];

  // State
  let slides = [];
  let currentSlideIndex = 0;
  let slideInterval = null;
  let isTransitioning = false;

  // Drag state
  let isDragging = false;
  let startX = 0;
  let currentX = 0;
  let dragOffset = 0;
  let sliderTrack = null;
  let hasDragged = false;

  // Touch gesture detection state
  let touchStartX = 0;
  let touchStartY = 0;
  let gestureDetected = false;
  let isVerticalGesture = false;
  let touchStartedOnSlider = false; // Track if touch started on slider
  const GESTURE_THRESHOLD = 10; // Minimum movement to detect gesture direction

  // DOM Elements
  const sliderContainer = document.querySelector(`.slider-container`);
  const dotsContainer = document.querySelector(`.slider-dots`);
  const heroSlider = document.querySelector(`.hero-slider`);



  // API Functions
  async function fetchSlides() {
    try {
      console.log(`API\`den slaytlar alınıyor...`);
      
      const response = await fetch(SLIDER_API_URL, {
        method: `GET`,
        headers: {
          [`Content-Type`]: `application/json`,
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.sliders || !Array.isArray(data.sliders)) {
        throw new Error(`Invalid response format`);
      }

      return data.sliders.filter(s => s.type === SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE);
    } catch (error) {
      console.error(`Slaytlar alınırken hata:`, error);
      throw error;
    }
  }

  // Slide Management
  function createSlideElement(slide, index) {
    const slideDiv = document.createElement(`div`);
    slideDiv.className = `slide`;
    slideDiv.id = `slide-${index}`;

    const img = document.createElement(`img`);
    // Handle both mocked data (full path) and API data (path only)
    if (USE_MOCKED_DATA || slide.path.startsWith(`/`)) {
      img.src = slide.path;
    } else {
      // img.src = `${IMAGE_BASE_URL}${slide.path}`;
      img.src = slide.path.startsWith('http') ? slide.path : `${IMAGE_BASE_URL}${slide.path}`;
    }
    img.alt = slide.content || `Slayt ${index + 1}`;
    img.className = `slide-image`;

    // Add click handler if URL is provided
    if (slide.url) {
      slideDiv.style.cursor = `pointer`;

      let isScrolling = false;
      let touchStartY = 0;

      const handleSlideNavigation = (e) => {
        // Prevent navigation if user has dragged
        if (hasDragged) {
          e.preventDefault();
          e.stopPropagation();
          return;
        }

        if (slide.url.startsWith(`/`)) {
          $LANDING.navigate(slide.url);
        } else {
          window.open(slide.url, `_blank`);
        }
      };

      slideDiv.addEventListener(`click`, handleSlideNavigation);

      // Add touch support with scroll detection
      slideDiv.addEventListener(`touchstart`, (e) => {
        isScrolling = false;
        touchStartY = e.touches[0].clientY;
      });

      slideDiv.addEventListener(`touchmove`, (e) => {
        const touchMoveY = e.touches[0].clientY;
        const deltaY = Math.abs(touchMoveY - touchStartY);

        if (deltaY > 10) {
          isScrolling = true;
        }
      });

      slideDiv.addEventListener(`touchend`, (e) => {
        if (isScrolling) {
          return;
        }

        e.preventDefault();
        handleSlideNavigation(e);
      });
    }

    slideDiv.appendChild(img);
    return slideDiv;
  }

  function createDotElement(index) {
    const dot = document.createElement(`div`);
    dot.className = `slider-dot`;
    dot.setAttribute(`data-slide-index`, index);

    // Add click handler with proper event handling
    let isScrolling = false;
    let touchStartY = 0;

    const handleDotClick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`Dot clicked for slide ${index}`);
      goToSlide(index);
    };

    dot.addEventListener(`click`, handleDotClick);

    // Add touch support with scroll detection
    dot.addEventListener(`touchstart`, (e) => {
      isScrolling = false;
      touchStartY = e.touches[0].clientY;
    });

    dot.addEventListener(`touchmove`, (e) => {
      const touchMoveY = e.touches[0].clientY;
      const deltaY = Math.abs(touchMoveY - touchStartY);

      if (deltaY > 10) {
        isScrolling = true;
      }
    });

    dot.addEventListener(`touchend`, (e) => {
      if (isScrolling) {
        return;
      }

      e.preventDefault();
      handleDotClick(e);
    });

    // Add keyboard support
    dot.setAttribute(`tabindex`, `0`);
    dot.setAttribute(`role`, `button`);
    dot.setAttribute(`aria-label`, `${index + 1}. slayta git`);

    dot.addEventListener(`keydown`, (e) => {
      if (e.key === `Enter` || e.key === ` `) {
        e.preventDefault();
        goToSlide(index);
      }
    });

    return dot;
  }

  function renderSlides() {
    if (!slides.length) return;

    console.log(`${slides.length} slayt oluşturuluyor...`);

    // Clear existing slides and reset state
    sliderContainer.innerHTML = ``;
    dotsContainer.innerHTML = ``;
    currentSlideIndex = 0;
    isTransitioning = false;

    // Create slider track
    sliderTrack = document.createElement(`div`);
    sliderTrack.className = `slider-track`;
    sliderContainer.appendChild(sliderTrack);

    // Create slides and dots
    slides.forEach((slide, index) => {
      const slideElement = createSlideElement(slide, index);
      sliderTrack.appendChild(slideElement);

      const dotElement = createDotElement(index);
      dotsContainer.appendChild(dotElement);
    });

    console.log(`${slides.length} slayt ve ${dotsContainer.children.length} nokta oluşturuldu`);

    // Initialize slider position
    updateSliderPosition();

    // Setup drag functionality
    setupDragHandlers();
  }

  function updateSliderPosition() {
    if (!sliderTrack || slides.length === 0) return;

    const translateX = -currentSlideIndex * 100;
    sliderTrack.style.transform = `translateX(${translateX}%)`;

    // Update dots
    const allDots = dotsContainer.querySelectorAll(`.slider-dot`);
    allDots.forEach((dot, i) => {
      if (i === currentSlideIndex) {
        dot.classList.add(`active`);
      } else {
        dot.classList.remove(`active`);
      }
    });


  }

  function goToSlide(index) {
    if (isTransitioning || index === currentSlideIndex || index < 0 || index >= slides.length) return;

    console.log(`Switching from slide ${currentSlideIndex} to slide ${index}`);

    isTransitioning = true;
    currentSlideIndex = index;

    updateSliderPosition();

    // Reset auto-slide timer
    startAutoSlide();

    // Reset transition flag after animation
    setTimeout(() => {
      isTransitioning = false;
      console.log(`Slide transition completed. Current slide: ${currentSlideIndex}`);
    }, 600);
  }

  function nextSlide() {
    if (slides.length === 0) return;

    const nextIndex = (currentSlideIndex + 1) % slides.length;
    console.log(`Auto-advancing to slide ${nextIndex}`);
    goToSlide(nextIndex);
  }

  function setupDragHandlers() {
    if (!sliderTrack) return;

    // Mouse events
    sliderTrack.addEventListener('mousedown', handleDragStart);
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);

    // Touch events with gesture detection
    sliderTrack.addEventListener('touchstart', handleTouchStart);
    heroSlider.addEventListener('touchmove', handleTouchMove, { passive: false });
    heroSlider.addEventListener('touchend', handleTouchEnd);

    // Prevent default drag behavior on images
    sliderTrack.addEventListener('dragstart', (e) => e.preventDefault());
  }

  function getEventX(e) {
    return e.type.includes('mouse') ? e.clientX : e.touches[0].clientX;
  }

  function handleDragStart(e) {
    if (isTransitioning) return;

    isDragging = true;
    hasDragged = false;
    startX = getEventX(e);
    currentX = startX;
    dragOffset = 0;

    sliderTrack.classList.add('dragging');
    stopAutoSlide();
  }

  function handleDragMove(e) {
    if (!isDragging) return;

    currentX = getEventX(e);
    dragOffset = currentX - startX;

    // Mark that user has dragged if they moved more than a few pixels
    if (Math.abs(dragOffset) > 5) {
      hasDragged = true;
    }

    // Calculate the current position with drag offset
    const baseTranslateX = -currentSlideIndex * 100;
    const dragPercentage = (dragOffset / sliderContainer.offsetWidth) * 100;
    const newTranslateX = baseTranslateX + dragPercentage;

    sliderTrack.style.transform = `translateX(${newTranslateX}%)`;

    e.preventDefault();
  }

  function handleDragEnd(e) {
    if (!isDragging) return;

    isDragging = false;
    sliderTrack.classList.remove('dragging');

    const threshold = sliderContainer.offsetWidth * 0.2; // 20% threshold
    let newIndex = currentSlideIndex;

    if (Math.abs(dragOffset) > threshold) {
      if (dragOffset > 0 && currentSlideIndex > 0) {
        // Dragged right, go to previous slide
        newIndex = currentSlideIndex - 1;
      } else if (dragOffset < 0 && currentSlideIndex < slides.length - 1) {
        // Dragged left, go to next slide
        newIndex = currentSlideIndex + 1;
      }
    }

    // Reset drag state
    dragOffset = 0;

    if (newIndex !== currentSlideIndex) {
      goToSlide(newIndex);
    } else {
      // Snap back to current position
      updateSliderPosition();
      startAutoSlide();
    }

    // Reset hasDragged flag after a short delay to prevent click events
    setTimeout(() => {
      hasDragged = false;
    }, 100);

    // Reset gesture detection state
    gestureDetected = false;
    isVerticalGesture = false;
    touchStartedOnSlider = false;

    e.preventDefault();
  }

  // Touch event handlers with gesture detection
  function handleTouchStart(e) {
    if (isTransitioning || e.touches.length !== 1) return;

    // Initialize touch tracking
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
    gestureDetected = false;
    isVerticalGesture = false;
    touchStartedOnSlider = true;

    // Start as horizontal gesture by default - but don't prevent default yet
    // e.preventDefault(); // Don't block scroll on touchstart
    handleDragStart(e);
  }

  function handleTouchMove(e) {
    // Only handle touch events that started on the slider
    if (e.touches.length === 1 && touchStartedOnSlider) {
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;

      // If we haven't detected gesture direction yet
      if (!gestureDetected) {
        const deltaX = Math.abs(currentX - touchStartX);
        const deltaY = Math.abs(currentY - touchStartY);

        // Check if movement exceeds threshold
        if (deltaX > GESTURE_THRESHOLD || deltaY > GESTURE_THRESHOLD) {
          gestureDetected = true;
          isVerticalGesture = deltaY > deltaX;

          // If vertical gesture detected, stop dragging and allow scrolling
          if (isVerticalGesture) {
            if (isDragging) {
              handleDragEnd(e);
            }
            touchStartedOnSlider = false; // Stop handling this touch sequence
            return; // Don't prevent default, allow vertical scrolling
          }
        }
      }

      // If we're in horizontal drag mode (not vertical), continue dragging
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        handleDragMove(e);
      }
    }
  }

  function handleTouchEnd(e) {
    // Only handle if touch started on slider
    if (touchStartedOnSlider) {
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        handleDragEnd(e);
      }

      // Reset gesture detection state
      gestureDetected = false;
      isVerticalGesture = false;
      touchStartedOnSlider = false;
    }
  }

  function startAutoSlide() {
    // Clear any existing interval
    if (slideInterval) {
      clearInterval(slideInterval);
      slideInterval = null;
    }

    // Only start auto-slide if we have multiple slides
    if (slides.length > 1) {
      slideInterval = setInterval(nextSlide, SLIDE_DURATION);
      console.log(`Auto-slide started with ${SLIDE_DURATION}ms interval`);
    }
  }

  function stopAutoSlide() {
    if (slideInterval) {
      clearInterval(slideInterval);
      slideInterval = null;
    }
  }

  // Loading States
  function setLoadingState() {
    heroSlider.classList.add(`loading`);
    heroSlider.classList.remove(`error`);
  }

  function setErrorState() {
    heroSlider.classList.add(`error`);
    heroSlider.classList.remove(`loading`);
  }

  function clearStates() {
    heroSlider.classList.remove(`loading`, `error`);
  }

  // Initialization
  async function initializeSlider() {
    try {
      setLoadingState();

      let slidesData;

      if (USE_MOCKED_DATA) {
        // Use mocked data
        console.log(`Sahte slayt verileri kullanılıyor`);
        slidesData = MOCKED_SLIDES;
      } else {
        // Fetch from API
        slidesData = await fetchSlides();
      }

      if (!slidesData.length) {
        throw new Error(`Slayt bulunamadı`);
      }

      slides = slidesData;
      clearStates();
      renderSlides();
      startAutoSlide();

      console.log(`Hero slider başarıyla başlatıldı`);

    } catch (error) {
      console.error(`Hero slider başlatılamadı:`, error);
      setErrorState();
    }
  }

  // Event Listeners
  function setupEventListeners() {
    // Pause auto-slide on hover
    if (heroSlider) {
      heroSlider.addEventListener(`mouseenter`, stopAutoSlide);
      heroSlider.addEventListener(`mouseleave`, startAutoSlide);
    }
    
    // Handle visibility change
    document.addEventListener(`visibilitychange`, () => {
      if (document.hidden) {
        stopAutoSlide();
      } else {
        startAutoSlide();
      }
    });
  }

  // Function to check if user is authenticated
  function isUserAuthenticated() {
    return window.localStorage.getItem("LoggedIn") === "true";
  }

  // Function to highlight navigation section
  function highlightNavigationSection() {
    const navigationSection = document.querySelector('.navigation');
    if (!navigationSection) {
      console.warn('Navigation section not found');
      return;
    }

    console.log('Adding highlight class to navigation section');

    // Add highlight class
    navigationSection.classList.add('highlight-games');

    // Scroll to navigation section smoothly with a small delay to ensure visibility
    setTimeout(() => {
      navigationSection.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      console.log('Scrolled to navigation section');
    }, 100);

    // Remove highlight after 4 seconds (increased duration)
    setTimeout(() => {
      navigationSection.classList.remove('highlight-games');
      console.log('Removed highlight class from navigation section');
    }, 2000);

    console.log('Navigation section highlighted for authenticated user');
  }

  // Function to setup hero CTA button handler
  function setupHeroCTAHandler() {
    console.log("Setup Hero CTA handler");

    const heroCTAButton = document.querySelector('.hero-cta-button');
    if (!heroCTAButton) {
      console.warn('Hero CTA button not found');
      return;
    }

    // Store original click handler
    const originalNavigatePath = heroCTAButton.getAttribute('data-navigate');

    let isScrolling = false;
    let touchStartY = 0;

    const handleCTAClick = (e) => {
      e.preventDefault();
      e.stopPropagation();

      console.log('Hero CTA button clicked');

      // Check if user is authenticated
      const isAuthenticated = isUserAuthenticated();
      console.log('User authentication status:', isAuthenticated);

      if (isAuthenticated) {
        // User is authenticated, highlight navigation section
        console.log('User is authenticated, highlighting navigation section');
        highlightNavigationSection();
      } else {
        // User is not authenticated, proceed with original navigation
        console.log('User is not authenticated, proceeding to signup');
        if (originalNavigatePath) {
          $LANDING.navigate(originalNavigatePath);
        }
      }
    };

    // Remove existing data-navigate to prevent default navigation
    heroCTAButton.removeAttribute('data-navigate');

    // Add our custom click handler
    heroCTAButton.addEventListener('click', handleCTAClick);

    // Add touch support with scroll detection
    heroCTAButton.addEventListener('touchstart', (e) => {
      isScrolling = false;
      touchStartY = e.touches[0].clientY;
    });

    heroCTAButton.addEventListener('touchmove', (e) => {
      const touchMoveY = e.touches[0].clientY;
      const deltaY = Math.abs(touchMoveY - touchStartY);

      if (deltaY > 10) {
        isScrolling = true;
      }
    });

    heroCTAButton.addEventListener('touchend', (e) => {
      if (isScrolling) {
        return;
      }

      e.preventDefault();
      handleCTAClick(e);
    });

    console.log('Hero CTA button handler setup complete');
  }

  // Start initialization
  function initialize() {
    if (!sliderContainer || !dotsContainer || !heroSlider) {
      console.warn(`Hero slider elements not found`);
      return;
    }

    setupEventListeners();
    initializeSlider();
    setupHeroCTAHandler();
  }

  // Initialize when DOM is ready
  initialize();

  window.addEventListener('@makrobet/unload/landing', () => {
    kill()
    stopAutoSlide()
  }, { once: true })
  
  return () => {
    stopAutoSlide()
  }
}});
