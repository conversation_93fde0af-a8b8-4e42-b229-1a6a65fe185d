{"name": "Makrobet Extension", "description": "Makrobet Extension", "version": "1.0", "manifest_version": 3, "action": {"default_popup": "index.html", "default_icon": "icon.png"}, "icons": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}, "permissions": ["declarativeNetRequest"], "host_permissions": ["https://d11caojl6op1au.cloudfront.net/*", "https://makrobet681.com/*"], "background": {"service_worker": "background.js"}, "declarative_net_request": {"rule_resources": [{"id": "block_rules", "enabled": true, "path": "rules.json"}]}, "content_scripts": [{"js": ["core.js", "scripts/landing.js", "scripts/header.js", "scripts/sidebar.js", "scripts/vip-page.js"], "matches": ["https://makrobet681.com/*", "https://m.makrobet681.com/*"]}]}